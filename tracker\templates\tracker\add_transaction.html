{% extends 'tracker/base.html' %}

{% block title %}Add {{ transaction_type|title }} - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Add {{ transaction_type|title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'transaction_list' %}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to Transactions
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas {% if transaction_type == 'income' %}fa-plus-circle text-success{% else %}fa-minus-circle text-danger{% endif %} me-2"></i>
                    Add {{ transaction_type|title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">Title *</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger">{{ form.title.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.amount.id_for_label }}" class="form-label">Amount *</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.amount }}
                            </div>
                            {% if form.amount.errors %}
                                <div class="text-danger">{{ form.amount.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.date.id_for_label }}" class="form-label">Date *</label>
                            {{ form.date }}
                            {% if form.date.errors %}
                                <div class="text-danger">{{ form.date.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.type.id_for_label }}" class="form-label">Type *</label>
                            {{ form.type }}
                            {% if form.type.errors %}
                                <div class="text-danger">{{ form.type.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">Category *</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="text-danger">{{ form.category.errors }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Don't see your category? <a href="{% url 'add_category' %}" target="_blank">Add a new one</a>
                            </small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">{{ form.description.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'transaction_list' %}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn {% if transaction_type == 'income' %}btn-success{% else %}btn-danger{% endif %}">
                            <i class="fas fa-save"></i> Save {{ transaction_type|title }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update category options based on transaction type
    document.getElementById('id_type').addEventListener('change', function() {
        const type = this.value;
        const categorySelect = document.getElementById('id_category');
        
        // Clear current options
        categorySelect.innerHTML = '<option value="">---------</option>';
        
        // This would need to be implemented with AJAX in a real application
        // For now, we'll just show a message
        if (type) {
            console.log('Transaction type changed to:', type);
        }
    });
</script>
{% endblock %}