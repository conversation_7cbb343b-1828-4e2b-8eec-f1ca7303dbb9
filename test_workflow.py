#!/usr/bin/env python
"""
Test script to verify the Django financial tracker application workflow
"""
import os
import sys
import django
from decimal import Decimal
from datetime import date, datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'financial_tracker.settings')
django.setup()

from django.contrib.auth.models import User
from tracker.models import Category, Transaction, Budget

def test_application():
    print("🧪 Testing Django Financial Tracker Application")
    print("=" * 50)
    
    # Test 1: Check categories
    print("\n1. Testing Categories...")
    categories = Category.objects.all()
    income_categories = categories.filter(type='income')
    expense_categories = categories.filter(type='expense')
    
    print(f"   ✅ Total categories: {categories.count()}")
    print(f"   ✅ Income categories: {income_categories.count()}")
    print(f"   ✅ Expense categories: {expense_categories.count()}")
    
    # Test 2: Create test user
    print("\n2. Testing User Creation...")
    test_user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print("   ✅ Test user created successfully")
    else:
        print("   ✅ Test user already exists")
    
    # Test 3: Create sample transactions
    print("\n3. Testing Transaction Creation...")
    
    # Get some categories
    salary_category = income_categories.filter(name='Salary').first()
    groceries_category = expense_categories.filter(name='Groceries').first()
    
    if salary_category and groceries_category:
        # Create income transaction
        income_transaction, created = Transaction.objects.get_or_create(
            user=test_user,
            title='Monthly Salary',
            amount=Decimal('5000.00'),
            type='income',
            category=salary_category,
            defaults={
                'description': 'Monthly salary payment',
                'date': date.today()
            }
        )
        if created:
            print("   ✅ Income transaction created")
        else:
            print("   ✅ Income transaction already exists")
        
        # Create expense transaction
        expense_transaction, created = Transaction.objects.get_or_create(
            user=test_user,
            title='Weekly Groceries',
            amount=Decimal('150.00'),
            type='expense',
            category=groceries_category,
            defaults={
                'description': 'Weekly grocery shopping',
                'date': date.today()
            }
        )
        if created:
            print("   ✅ Expense transaction created")
        else:
            print("   ✅ Expense transaction already exists")
    else:
        print("   ❌ Required categories not found")
    
    # Test 4: Create budget
    print("\n4. Testing Budget Creation...")
    if groceries_category:
        budget, created = Budget.objects.get_or_create(
            user=test_user,
            category=groceries_category,
            month=date.today().replace(day=1),
            defaults={'amount': Decimal('600.00')}
        )
        if created:
            print("   ✅ Budget created successfully")
        else:
            print("   ✅ Budget already exists")
        
        # Test budget calculations
        spent = budget.spent_amount()
        remaining = budget.remaining_amount()
        over_budget = budget.is_over_budget()
        
        print(f"   ✅ Budget amount: ${budget.amount}")
        print(f"   ✅ Spent amount: ${spent}")
        print(f"   ✅ Remaining: ${remaining}")
        print(f"   ✅ Over budget: {over_budget}")
    
    # Test 5: Verify calculations
    print("\n5. Testing Financial Calculations...")
    user_transactions = Transaction.objects.filter(user=test_user)
    total_income = sum(t.amount for t in user_transactions.filter(type='income'))
    total_expenses = sum(t.amount for t in user_transactions.filter(type='expense'))
    balance = total_income - total_expenses
    
    print(f"   ✅ Total income: ${total_income}")
    print(f"   ✅ Total expenses: ${total_expenses}")
    print(f"   ✅ Balance: ${balance}")
    
    # Test 6: Check admin user
    print("\n6. Testing Admin User...")
    try:
        admin_user = User.objects.get(username='admin')
        print(f"   ✅ Admin user exists: {admin_user.username}")
        print(f"   ✅ Admin email: {admin_user.email}")
        print(f"   ✅ Is superuser: {admin_user.is_superuser}")
    except User.DoesNotExist:
        print("   ❌ Admin user not found")
    
    print("\n" + "=" * 50)
    print("🎉 Application testing completed successfully!")
    print("\nYou can now:")
    print("1. Visit http://127.0.0.1:8000/ in your browser")
    print("2. Register a new account or login with:")
    print("   - Username: admin")
    print("   - Password: admin123")
    print("3. Test all the features:")
    print("   - Dashboard with financial overview")
    print("   - Add/view transactions")
    print("   - Manage categories")
    print("   - Set up budgets")
    print("   - View financial reports")

if __name__ == '__main__':
    test_application()
