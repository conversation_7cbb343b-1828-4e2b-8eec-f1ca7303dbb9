{% extends 'tracker/base.html' %}

{% block title %}Delete Transaction - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Delete Transaction</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'transaction_list' %}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to Transactions
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Confirm Deletion
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-warning me-2"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>Are you sure you want to delete this transaction?</p>
                
                <!-- Transaction Details -->
                <div class="card bg-light mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-3"><strong>Title:</strong></div>
                            <div class="col-sm-9">{{ transaction.title }}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-3"><strong>Amount:</strong></div>
                            <div class="col-sm-9">
                                <span class="{% if transaction.type == 'income' %}income{% else %}expense{% endif %}">
                                    {% if transaction.type == 'expense' %}-{% endif %}${{ transaction.amount|floatformat:2 }}
                                </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-3"><strong>Type:</strong></div>
                            <div class="col-sm-9">
                                <span class="badge {% if transaction.type == 'income' %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ transaction.get_type_display }}
                                </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-3"><strong>Category:</strong></div>
                            <div class="col-sm-9">{{ transaction.category.name }}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-3"><strong>Date:</strong></div>
                            <div class="col-sm-9">{{ transaction.date|date:"M d, Y" }}</div>
                        </div>
                        {% if transaction.description %}
                        <div class="row">
                            <div class="col-sm-3"><strong>Description:</strong></div>
                            <div class="col-sm-9">{{ transaction.description }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <form method="post">
                    {% csrf_token %}
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'transaction_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Delete Transaction
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
