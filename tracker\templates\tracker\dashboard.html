{% extends 'tracker/base.html' %}

{% block title %}Dashboard - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'add_transaction' %}?type=income" class="btn btn-success btn-sm">
                <i class="fas fa-plus"></i> Add Income
            </a>
            <a href="{% url 'add_transaction' %}?type=expense" class="btn btn-danger btn-sm">
                <i class="fas fa-minus"></i> Add Expense
            </a>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title income">
                    <i class="fas fa-arrow-up"></i> Total Income
                </h5>
                <h3 class="income">${{ total_income|floatformat:2 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title expense">
                    <i class="fas fa-arrow-down"></i> Total Expenses
                </h5>
                <h3 class="expense">${{ total_expenses|floatformat:2 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-balance-scale"></i> Balance
                </h5>
                <h3 class="{% if balance >= 0 %}balance-positive{% else %}balance-negative{% endif %}">
                    ${{ balance|floatformat:2 }}
                </h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-calendar-alt"></i> This Month
                </h5>
                <p class="mb-1 income">Income: ${{ month_income|floatformat:2 }}</p>
                <p class="mb-0 expense">Expenses: ${{ month_expenses|floatformat:2 }}</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Transactions -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Transactions</h5>
                <a href="{% url 'transaction_list' %}" class="btn btn-outline-primary btn-sm">View All</a>
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.date|date:"M d" }}</td>
                                    <td>{{ transaction.title }}</td>
                                    <td>{{ transaction.category.name }}</td>
                                    <td class="{% if transaction.type == 'income' %}income{% else %}expense{% endif %}">
                                        {% if transaction.type == 'expense' %}-{% endif %}${{ transaction.amount|floatformat:2 }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No transactions yet. <a href="{% url 'add_transaction' %}">Add your first transaction</a>.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Budget Status -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Budget Status</h5>
                <a href="{% url 'budget_list' %}" class="btn btn-outline-primary btn-sm">Manage</a>
            </div>
            <div class="card-body">
                {% if budgets %}
                    {% for budget in budgets %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ budget.category.name }}</span>
                            <span class="{% if budget.is_over_budget %}text-danger{% endif %}">
                                ${{ budget.spent_amount|floatformat:2 }} / ${{ budget.amount|floatformat:2 }}
                            </span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            {% widthratio budget.spent_amount budget.amount 100 as percentage %}
                            <div class="progress-bar {% if budget.is_over_budget %}bg-danger{% else %}bg-primary{% endif %}" 
                                 style="width: {% if percentage > 100 %}100{% else %}{{ percentage }}{% endif %}%"></div>
                        </div>
                        {% if budget.is_over_budget %}
                            <small class="text-danger">Over budget by ${{ budget.spent_amount|floatformat:2 }}</small>
                        {% else %}
                            <small class="text-muted">Remaining: ${{ budget.remaining_amount|floatformat:2 }}</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No budgets set. <a href="{% url 'add_budget' %}">Create your first budget</a>.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}