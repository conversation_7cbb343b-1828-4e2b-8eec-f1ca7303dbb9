{% extends 'tracker/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Dashboard" %} - {% trans "Premium Financial Tracker" %}{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4 animate-fade-in-up">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1 fw-bold text-dark">{% trans "Financial Dashboard" %}</h1>
                <p class="text-muted mb-0">{% trans "Your complete financial overview" %}</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'add_transaction' %}?type=income" class="btn btn-success-premium">
                    <i class="fas fa-plus-circle"></i>
                    <span>{% trans "Add Income" %}</span>
                </a>
                <a href="{% url 'add_transaction' %}?type=expense" class="btn btn-danger-premium">
                    <i class="fas fa-minus-circle"></i>
                    <span>{% trans "Add Expense" %}</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Premium Stats Cards -->
<div class="row mb-5 animate-slide-in-right">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card income">
            <div class="stats-icon income">
                <i class="fas fa-arrow-trend-up"></i>
            </div>
            <div class="d-flex justify-content-between align-items-end">
                <div>
                    <h6 class="text-muted mb-1 fw-medium">{% trans "Total Income" %}</h6>
                    <h2 class="mb-0 fw-bold" data-counter="{{ total_income }}">
                        ${{ total_income|floatformat:2 }}
                    </h2>
                </div>
                <div class="text-success">
                    <i class="fas fa-arrow-up"></i>
                    <small class="fw-medium">+12.5%</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card expense">
            <div class="stats-icon expense">
                <i class="fas fa-arrow-trend-down"></i>
            </div>
            <div class="d-flex justify-content-between align-items-end">
                <div>
                    <h6 class="text-muted mb-1 fw-medium">{% trans "Total Expenses" %}</h6>
                    <h2 class="mb-0 fw-bold" data-counter="{{ total_expenses }}">
                        ${{ total_expenses|floatformat:2 }}
                    </h2>
                </div>
                <div class="text-warning">
                    <i class="fas fa-arrow-up"></i>
                    <small class="fw-medium">+8.2%</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card balance">
            <div class="stats-icon balance">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="d-flex justify-content-between align-items-end">
                <div>
                    <h6 class="text-muted mb-1 fw-medium">{% trans "Current Balance" %}</h6>
                    <h2 class="mb-0 fw-bold {% if balance >= 0 %}text-success{% else %}text-danger{% endif %}" data-counter="{{ balance }}">
                        ${{ balance|floatformat:2 }}
                    </h2>
                </div>
                <div class="{% if balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <i class="fas fa-{% if balance >= 0 %}arrow-up{% else %}arrow-down{% endif %}"></i>
                    <small class="fw-medium">{% if balance >= 0 %}+{% endif %}{{ balance|floatformat:0 }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: var(--warning-gradient);">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div>
                <h6 class="text-muted mb-1 fw-medium">{% trans "This Month" %}</h6>
                <div class="d-flex justify-content-between mb-1">
                    <small class="text-success">{% trans "Income" %}:</small>
                    <small class="fw-bold">${{ month_income|floatformat:2 }}</small>
                </div>
                <div class="d-flex justify-content-between">
                    <small class="text-danger">{% trans "Expenses" %}:</small>
                    <small class="fw-bold">${{ month_expenses|floatformat:2 }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row animate-fade-in-up">
    <!-- Recent Transactions -->
    <div class="col-lg-8 mb-4">
        <div class="premium-card">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center p-4">
                <div>
                    <h5 class="mb-1 fw-bold">{% trans "Recent Transactions" %}</h5>
                    <p class="text-muted mb-0 small">{% trans "Your latest financial activities" %}</p>
                </div>
                <a href="{% url 'transaction_list' %}" class="btn btn-premium btn-sm">
                    <i class="fas fa-eye"></i>
                    {% trans "View All" %}
                </a>
            </div>
            <div class="card-body p-0">
                {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="premium-table table mb-0">
                            <thead>
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Title" %}</th>
                                    <th>{% trans "Category" %}</th>
                                    <th class="text-end">{% trans "Amount" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-light rounded-circle p-2 me-3">
                                                <i class="fas fa-calendar text-muted"></i>
                                            </div>
                                            <span class="fw-medium">{{ transaction.date|date:"M d, Y" }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-medium">{{ transaction.title }}</div>
                                        <small class="text-muted">{{ transaction.description|truncatechars:30 }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ transaction.category.name }}</span>
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold {% if transaction.type == 'income' %}text-success{% else %}text-danger{% endif %}">
                                            {% if transaction.type == 'expense' %}-{% else %}+{% endif %}${{ transaction.amount|floatformat:2 }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">{% trans "No transactions yet" %}</h6>
                        <p class="text-muted mb-3">{% trans "Start tracking your finances by adding your first transaction" %}</p>
                        <a href="{% url 'add_transaction' %}" class="btn btn-premium">
                            <i class="fas fa-plus"></i>
                            {% trans "Add Transaction" %}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Budget Status -->
    <div class="col-lg-4 mb-4">
        <div class="premium-card">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center p-4">
                <div>
                    <h5 class="mb-1 fw-bold">{% trans "Budget Status" %}</h5>
                    <p class="text-muted mb-0 small">{% trans "Track your spending limits" %}</p>
                </div>
                <a href="{% url 'budget_list' %}" class="btn btn-premium btn-sm">
                    <i class="fas fa-cog"></i>
                    {% trans "Manage" %}
                </a>
            </div>
            <div class="card-body p-4">
                {% if budgets %}
                    {% for budget in budgets %}
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="d-flex align-items-center">
                                <div class="bg-light rounded-circle p-2 me-3">
                                    <i class="fas fa-tag text-muted"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 fw-medium">{{ budget.category.name }}</h6>
                                    <small class="text-muted">{% trans "Monthly Budget" %}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold {% if budget.is_over_budget %}text-danger{% else %}text-dark{% endif %}">
                                    ${{ budget.spent_amount|floatformat:2 }}
                                </div>
                                <small class="text-muted">/ ${{ budget.amount|floatformat:2 }}</small>
                            </div>
                        </div>

                        <div class="progress-premium mb-2">
                            {% widthratio budget.spent_amount budget.amount 100 as percentage %}
                            <div class="progress-bar-premium {% if budget.is_over_budget %}danger{% elif percentage > 80 %}warning{% else %}success{% endif %}"
                                 style="width: {% if percentage > 100 %}100{% else %}{{ percentage }}{% endif %}%"
                                 data-width="{% if percentage > 100 %}100{% else %}{{ percentage }}{% endif %}%"></div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            {% if budget.is_over_budget %}
                                <small class="text-danger fw-medium">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {% trans "Over budget" %}
                                </small>
                                <small class="text-danger fw-bold">
                                    +${{ budget.over_budget_amount|floatformat:2 }}
                                </small>
                            {% else %}
                                <small class="text-success fw-medium">
                                    <i class="fas fa-check-circle me-1"></i>
                                    {% trans "On track" %}
                                </small>
                                <small class="text-muted">
                                    {% trans "Remaining" %}: ${{ budget.remaining_amount|floatformat:2 }}
                                </small>
                            {% endif %}
                        </div>
                    </div>
                    {% if not forloop.last %}
                        <hr class="my-3">
                    {% endif %}
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">{% trans "No budgets set" %}</h6>
                        <p class="text-muted mb-3">{% trans "Create budgets to track your spending" %}</p>
                        <a href="{% url 'add_budget' %}" class="btn btn-premium">
                            <i class="fas fa-plus"></i>
                            {% trans "Create Budget" %}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}