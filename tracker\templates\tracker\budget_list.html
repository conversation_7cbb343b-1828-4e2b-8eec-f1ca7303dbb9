{% extends 'tracker/base.html' %}

{% block title %}Budgets - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Budget Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'add_budget' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add Budget
            </a>
        </div>
    </div>
</div>

<!-- Budget Overview Cards -->
<div class="row mb-4">
    {% if budgets %}
        {% for budget in budgets %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card {% if budget.is_over_budget %}border-danger{% endif %}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ budget.category.name }}</h6>
                    <small class="text-muted">{{ budget.month|date:"M Y" }}</small>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Spent:</span>
                        <span class="{% if budget.is_over_budget %}text-danger{% else %}text-primary{% endif %}">
                            ${{ budget.spent_amount|floatformat:2 }}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Budget:</span>
                        <span>${{ budget.amount|floatformat:2 }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>Remaining:</span>
                        <span class="{% if budget.remaining_amount < 0 %}text-danger{% else %}text-success{% endif %}">
                            ${{ budget.remaining_amount|floatformat:2 }}
                        </span>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="progress mb-2" style="height: 10px;">
                        {% widthratio budget.spent_amount budget.amount 100 as percentage %}
                        <div class="progress-bar {% if budget.is_over_budget %}bg-danger{% elif percentage > 80 %}bg-warning{% else %}bg-success{% endif %}" 
                             style="width: {% if percentage > 100 %}100{% else %}{{ percentage }}{% endif %}%"
                             role="progressbar" 
                             aria-valuenow="{{ percentage }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{ percentage|default:0 }}% used</small>
                        {% if budget.is_over_budget %}
                            <span class="badge bg-danger">Over Budget</span>
                        {% elif percentage > 80 %}
                            <span class="badge bg-warning">Almost Full</span>
                        {% else %}
                            <span class="badge bg-success">On Track</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                <h5>No budgets set</h5>
                <p class="text-muted">Create your first budget to start tracking your spending limits.</p>
                <a href="{% url 'add_budget' %}" class="btn btn-primary">Create Budget</a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Detailed Budget Table -->
{% if budgets %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Budget Details</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Month</th>
                        <th>Budget Amount</th>
                        <th>Spent</th>
                        <th>Remaining</th>
                        <th>Progress</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for budget in budgets %}
                    <tr class="{% if budget.is_over_budget %}table-danger{% endif %}">
                        <td>
                            <strong>{{ budget.category.name }}</strong>
                        </td>
                        <td>{{ budget.month|date:"M Y" }}</td>
                        <td>${{ budget.amount|floatformat:2 }}</td>
                        <td class="{% if budget.is_over_budget %}text-danger{% endif %}">
                            ${{ budget.spent_amount|floatformat:2 }}
                        </td>
                        <td class="{% if budget.remaining_amount < 0 %}text-danger{% else %}text-success{% endif %}">
                            ${{ budget.remaining_amount|floatformat:2 }}
                        </td>
                        <td>
                            {% widthratio budget.spent_amount budget.amount 100 as percentage %}
                            <div class="progress" style="height: 8px; width: 100px;">
                                <div class="progress-bar {% if budget.is_over_budget %}bg-danger{% elif percentage > 80 %}bg-warning{% else %}bg-success{% endif %}" 
                                     style="width: {% if percentage > 100 %}100{% else %}{{ percentage }}{% endif %}%">
                                </div>
                            </div>
                            <small>{{ percentage|default:0 }}%</small>
                        </td>
                        <td>
                            {% if budget.is_over_budget %}
                                <span class="badge bg-danger">Over Budget</span>
                            {% elif percentage > 80 %}
                                <span class="badge bg-warning">Almost Full</span>
                            {% else %}
                                <span class="badge bg-success">On Track</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" disabled>
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" disabled>
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <small class="text-muted d-block">Edit/Delete coming soon</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Budget Tips -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-lightbulb me-2"></i>Budget Tips
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Set realistic budget amounts</li>
                    <li><i class="fas fa-check text-success me-2"></i>Review and adjust monthly</li>
                    <li><i class="fas fa-check text-success me-2"></i>Track spending regularly</li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Use the 50/30/20 rule as a guide</li>
                    <li><i class="fas fa-check text-success me-2"></i>Include a buffer for unexpected expenses</li>
                    <li><i class="fas fa-check text-success me-2"></i>Celebrate when you stay on track!</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
