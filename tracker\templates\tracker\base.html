{% load static %}
{% load i18n %}
{% get_current_language as LANGUAGE_CODE %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "Premium Financial Tracker" %}{% endblock %}</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    {% if LANGUAGE_CODE == 'ar' %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Premium Styles -->
    <link href="{% static 'tracker/css/premium-style.css' %}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body class="{% if LANGUAGE_CODE == 'ar' %}rtl-layout{% endif %}">
    {% csrf_token %}
    {% if user.is_authenticated %}
    <!-- Premium Sidebar -->
    <nav class="premium-sidebar col-md-3 col-lg-2 d-md-block position-fixed h-100">
        <div class="sidebar-nav p-3">
            <!-- Logo and User Info -->
            <div class="text-center mb-4">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <i class="fas fa-wallet fa-2x text-white me-2"></i>
                    <h4 class="text-white mb-0">{% trans "FinTracker" %}</h4>
                </div>
                <div class="text-center">
                    <div class="bg-white bg-opacity-20 rounded-pill px-3 py-2 mb-2">
                        <small class="text-white">{% trans "Welcome" %}</small>
                        <div class="text-white fw-bold">{{ user.first_name|default:user.username }}</div>
                    </div>
                </div>
            </div>

            <!-- Language Switcher -->
            <div class="language-switcher mb-4 d-flex justify-content-center">
                <a href="#" data-lang="en" class="{% if LANGUAGE_CODE == 'en' %}active{% endif %}">EN</a>
                <span class="mx-2 text-white-50">|</span>
                <a href="#" data-lang="ar" class="{% if LANGUAGE_CODE == 'ar' %}active{% endif %}">عربي</a>
            </div>

            <!-- Navigation Menu -->
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link-premium {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard' %}">
                        <i class="fas fa-chart-line"></i>
                        <span>{% trans "Dashboard" %}</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link-premium {% if request.resolver_match.url_name == 'transaction_list' %}active{% endif %}" href="{% url 'transaction_list' %}">
                        <i class="fas fa-exchange-alt"></i>
                        <span>{% trans "Transactions" %}</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link-premium" href="{% url 'add_transaction' %}?type=income">
                        <i class="fas fa-plus-circle text-success"></i>
                        <span>{% trans "Add Income" %}</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link-premium" href="{% url 'add_transaction' %}?type=expense">
                        <i class="fas fa-minus-circle text-danger"></i>
                        <span>{% trans "Add Expense" %}</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link-premium {% if request.resolver_match.url_name == 'category_list' %}active{% endif %}" href="{% url 'category_list' %}">
                        <i class="fas fa-tags"></i>
                        <span>{% trans "Categories" %}</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link-premium {% if request.resolver_match.url_name == 'budget_list' %}active{% endif %}" href="{% url 'budget_list' %}">
                        <i class="fas fa-chart-pie"></i>
                        <span>{% trans "Budgets" %}</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link-premium {% if request.resolver_match.url_name == 'reports' %}active{% endif %}" href="{% url 'reports' %}">
                        <i class="fas fa-chart-bar"></i>
                        <span>{% trans "Reports" %}</span>
                    </a>
                </li>
                <li class="nav-item mt-4 pt-3 border-top border-white border-opacity-20">
                    <form method="post" action="{% url 'logout' %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="nav-link-premium border-0 bg-transparent w-100 text-start">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>{% trans "Logout" %}</span>
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="col-md-9 ms-sm-auto col-lg-10 offset-md-3 offset-lg-2 px-4">
    {% else %}
    <!-- Full width for non-authenticated users -->
    <main class="col-12 min-vh-100 d-flex align-items-center justify-content-center">
    {% endif %}
        {% if user.is_authenticated %}
        <div class="container-fluid py-4">
        {% endif %}
            <!-- Messages -->
            {% if messages %}
                <div class="row mb-4">
                    <div class="col-12">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show premium-card border-0" role="alert">
                                <div class="d-flex align-items-center">
                                    {% if message.tags == 'success' %}
                                        <i class="fas fa-check-circle text-success me-3"></i>
                                    {% elif message.tags == 'error' %}
                                        <i class="fas fa-exclamation-circle text-danger me-3"></i>
                                    {% elif message.tags == 'warning' %}
                                        <i class="fas fa-exclamation-triangle text-warning me-3"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle text-info me-3"></i>
                                    {% endif %}
                                    <span>{{ message }}</span>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Page Content -->
            {% block content %}
            {% endblock %}

        {% if user.is_authenticated %}
        </div>
        {% endif %}
    </main>

    <!-- Mobile Sidebar Overlay -->
    <div class="sidebar-overlay d-md-none"></div>

    <!-- Mobile Menu Toggle -->
    {% if user.is_authenticated %}
    <button class="btn btn-premium sidebar-toggle d-md-none position-fixed" style="top: 20px; {% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %}: 20px; z-index: 1001;">
        <i class="fas fa-bars"></i>
    </button>
    {% endif %}

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'tracker/js/premium-app.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>