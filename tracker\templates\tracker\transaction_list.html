{% extends 'tracker/base.html' %}

{% block title %}Transactions - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Transactions</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'add_transaction' %}?type=income" class="btn btn-success btn-sm">
                <i class="fas fa-plus"></i> Add Income
            </a>
            <a href="{% url 'add_transaction' %}?type=expense" class="btn btn-danger btn-sm">
                <i class="fas fa-minus"></i> Add Expense
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-2">
                <label for="type" class="form-label">Type</label>
                <select name="type" id="type" class="form-select form-select-sm">
                    <option value="">All Types</option>
                    <option value="income" {% if current_type == 'income' %}selected{% endif %}>Income</option>
                    <option value="expense" {% if current_type == 'expense' %}selected{% endif %}>Expense</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="category" class="form-label">Category</label>
                <select name="category" id="category" class="form-select form-select-sm">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}" {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" name="date_from" id="date_from" class="form-control form-control-sm" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" name="date_to" id="date_to" class="form-control form-control-sm" value="{{ date_to }}">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary btn-sm me-2">Filter</button>
                <a href="{% url 'transaction_list' %}" class="btn btn-outline-secondary btn-sm">Clear</a>
            </div>
        </form>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-body">
        {% if transactions %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr>
                            <td>{{ transaction.date|date:"M d, Y" }}</td>
                            <td>{{ transaction.title }}</td>
                            <td>{{ transaction.category.name }}</td>
                            <td>
                                <span class="badge {% if transaction.type == 'income' %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ transaction.get_type_display }}
                                </span>
                            </td>
                            <td class="{% if transaction.type == 'income' %}income{% else %}expense{% endif %}">
                                {% if transaction.type == 'expense' %}-{% endif %}${{ transaction.amount|floatformat:2 }}
                            </td>
                            <td>{{ transaction.description|truncatechars:50 }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'edit_transaction' transaction.pk %}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'delete_transaction' transaction.pk %}" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5>No transactions found</h5>
                <p class="text-muted">Start by adding your first transaction.</p>
                <a href="{% url 'add_transaction' %}" class="btn btn-primary">Add Transaction</a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}