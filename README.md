# Django Financial Tracker

A comprehensive personal finance management application built with Django 5.2.3, featuring income/expense tracking, budget management, and financial reporting.

## 🚀 Features

- **Dashboard**: Financial overview with key metrics and recent transactions
- **Transaction Management**: Add, edit, delete, and categorize income/expenses
- **Category Management**: Organize transactions into meaningful categories
- **Budget Planning**: Set monthly spending limits with progress tracking
- **Financial Reports**: Comprehensive analytics and insights
- **User Authentication**: Secure login system with user-specific data
- **Responsive Design**: Mobile-friendly Bootstrap 5 interface
- **Admin Panel**: Django admin interface for system management

## 📊 Demo Data

The application comes pre-populated with:
- **128 realistic transactions** spanning 6 months
- **23 categories** (6 income, 17 expense types)
- **9 monthly budgets** with progress tracking
- **$31,200** in total income
- **$15,689** in total expenses
- **$15,511** current balance

## 🔑 Access Credentials

### Test User Account
- **Username**: `test`
- **Password**: `********`
- **Email**: <EMAIL>

### Admin Account
- **Username**: `admin`
- **Password**: `admin123`
- **Email**: <EMAIL>

## 🌐 Application URLs

- **Main Application**: http://127.0.0.1:8000/
- **Admin Panel**: http://127.0.0.1:8000/admin/
- **Login Page**: http://127.0.0.1:8000/login/
- **Registration**: http://127.0.0.1:8000/register/

## 🛠️ Technical Stack

- **Backend**: Django 5.2.3
- **Database**: SQLite3 (development)
- **Frontend**: Bootstrap 5.1.3 + Font Awesome 6.0.0
- **Authentication**: Django's built-in auth system
- **Styling**: Custom CSS with responsive design

## 📁 Project Structure

```
django_1/
├── financial_tracker/          # Main Django project
│   ├── settings.py            # Project settings
│   ├── urls.py               # Main URL configuration
│   └── wsgi.py               # WSGI configuration
├── tracker/                   # Main application
│   ├── models.py             # Data models
│   ├── views.py              # View functions
│   ├── forms.py              # Form definitions
│   ├── urls.py               # App URL patterns
│   ├── admin.py              # Admin configuration
│   ├── templates/            # HTML templates
│   │   ├── tracker/          # App templates
│   │   └── registration/     # Auth templates
│   └── migrations/           # Database migrations
├── manage.py                 # Django management script
└── db.sqlite3               # SQLite database
```

## 🎯 Key Models

### Transaction
- User-specific income/expense records
- Categorized with amount, date, and description
- Supports both income and expense types

### Category
- Organizes transactions into meaningful groups
- Separate categories for income and expenses
- Used for budgeting and reporting

### Budget
- Monthly spending limits per category
- Automatic progress tracking
- Over-budget warnings and calculations

## 🔧 Running the Application

1. **Start the development server**:
   ```bash
   python manage.py runserver
   ```

2. **Access the application**:
   - Open http://127.0.0.1:8000/ in your browser
   - Login with test/******** or admin/admin123

3. **Explore features**:
   - View dashboard with financial overview
   - Add new transactions and categories
   - Set up budgets and monitor progress
   - Generate financial reports

## 📈 Sample Data Overview

The test user account includes realistic financial data:

### Income Sources
- Monthly salary: $4,500
- Freelance projects: $600-$1,200
- Total 6-month income: $31,200

### Expense Categories
- Housing: Rent, utilities, insurance
- Transportation: Gas, maintenance
- Food: Groceries, dining out
- Personal: Shopping, entertainment, fitness
- Healthcare: Medical expenses

### Budget Tracking
- 9 active budgets for current month
- Visual progress indicators
- Over-budget warnings when applicable

## 🎨 User Interface

- **Clean, modern design** with Bootstrap 5
- **Responsive layout** works on all devices
- **Color-coded transactions** (green=income, red=expenses)
- **Interactive charts** and progress bars
- **Intuitive navigation** with sidebar menu

## 🔒 Security Features

- User authentication required for all financial data
- User-specific data isolation
- CSRF protection on all forms
- Secure password handling
- Admin-only access to system management

## 📱 Mobile Support

The application is fully responsive and works seamlessly on:
- Desktop computers
- Tablets
- Mobile phones
- All modern web browsers

## 🎉 Ready for Use

The Django Financial Tracker is completely set up and ready for immediate use. All features have been tested and verified to work correctly with the provided test data.

**Start tracking your finances today!** 💰📊📈
