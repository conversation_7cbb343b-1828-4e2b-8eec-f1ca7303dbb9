/* Premium Financial Tracker Design System */

/* CSS Variables for Premium Theme */
:root {
  /* Primary Colors */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --primary-light: #e6fffa;
  
  /* Secondary Colors */
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --secondary-color: #f093fb;
  
  /* Success/Income Colors */
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --success-color: #10b981;
  --success-light: #d1fae5;
  
  /* Danger/Expense Colors */
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --danger-color: #ef4444;
  --danger-light: #fef2f2;
  
  /* Warning Colors */
  --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --warning-color: #f59e0b;
  --warning-light: #fffbeb;
  
  /* Neutral Colors */
  --dark-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --dark-color: #1f2937;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Background */
  --bg-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --bg-light: #ffffff;
  --bg-dark: #1a202c;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* Typography */
  --font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-family-ar: 'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
}

/* RTL Support */
[dir="rtl"] {
  font-family: var(--font-family-ar);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: var(--bg-gradient);
  color: var(--gray-800);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

[dir="rtl"] body {
  font-family: var(--font-family-ar);
}

/* Premium Card Styles */
.premium-card {
  background: var(--bg-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.premium-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-2xl);
}

/* Gradient Buttons */
.btn-premium {
  background: var(--primary-gradient);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.btn-premium:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
  text-decoration: none;
}

.btn-success-premium {
  background: var(--success-gradient);
}

.btn-danger-premium {
  background: var(--danger-gradient);
}

.btn-warning-premium {
  background: var(--warning-gradient);
}

/* Premium Sidebar */
.premium-sidebar {
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
}

.premium-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.1;
}

.sidebar-nav {
  position: relative;
  z-index: 1;
}

.nav-link-premium {
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  margin: 4px 12px;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.nav-link-premium:hover,
.nav-link-premium.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateX(5px);
  text-decoration: none;
}

[dir="rtl"] .nav-link-premium:hover,
[dir="rtl"] .nav-link-premium.active {
  transform: translateX(-5px);
}

/* Premium Stats Cards */
.stats-card {
  background: var(--bg-light);
  border-radius: var(--radius-xl);
  padding: 24px;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stats-card.income::before {
  background: var(--success-gradient);
}

.stats-card.expense::before {
  background: var(--danger-gradient);
}

.stats-card.balance::before {
  background: var(--primary-gradient);
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-bottom: 16px;
}

.stats-icon.income {
  background: var(--success-gradient);
}

.stats-icon.expense {
  background: var(--danger-gradient);
}

.stats-icon.balance {
  background: var(--primary-gradient);
}

/* Premium Tables */
.premium-table {
  background: var(--bg-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: none;
}

.premium-table thead {
  background: var(--primary-gradient);
  color: white;
}

.premium-table th {
  padding: 16px;
  font-weight: 600;
  border: none;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.05em;
}

.premium-table td {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.premium-table tbody tr:hover {
  background: var(--gray-50);
}

/* Premium Forms */
.premium-form {
  background: var(--bg-light);
  border-radius: var(--radius-xl);
  padding: 32px;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-control-premium {
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: var(--bg-light);
}

.form-control-premium:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

/* Premium Progress Bars */
.progress-premium {
  height: 12px;
  border-radius: var(--radius-lg);
  background: var(--gray-200);
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar-premium {
  height: 100%;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar-premium.success {
  background: var(--success-gradient);
}

.progress-bar-premium.warning {
  background: var(--warning-gradient);
}

.progress-bar-premium.danger {
  background: var(--danger-gradient);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

/* Language Switcher */
.language-switcher {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: 8px;
  backdrop-filter: blur(10px);
}

.language-switcher a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  padding: 6px 12px;
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  font-weight: 500;
}

.language-switcher a:hover,
.language-switcher a.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .premium-sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .premium-sidebar.show {
    left: 0;
  }
  
  [dir="rtl"] .premium-sidebar {
    left: auto;
    right: -100%;
  }
  
  [dir="rtl"] .premium-sidebar.show {
    right: 0;
  }
}

/* RTL Specific Styles */
[dir="rtl"] .stats-card {
  text-align: right;
}

[dir="rtl"] .d-flex {
  flex-direction: row-reverse;
}

[dir="rtl"] .me-2,
[dir="rtl"] .me-3 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

[dir="rtl"] .ms-2,
[dir="rtl"] .ms-3 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

[dir="rtl"] .text-end {
  text-align: left !important;
}

[dir="rtl"] .text-start {
  text-align: right !important;
}

[dir="rtl"] .premium-table th,
[dir="rtl"] .premium-table td {
  text-align: right;
}

[dir="rtl"] .premium-table th:last-child,
[dir="rtl"] .premium-table td:last-child {
  text-align: left;
}

/* Mobile Enhancements */
@media (max-width: 576px) {
  .stats-card {
    margin-bottom: 1rem;
  }

  .btn-premium {
    padding: 8px 16px;
    font-size: 14px;
  }

  .premium-card {
    margin-bottom: 1rem;
  }

  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  h2 {
    font-size: 1.5rem;
  }

  .premium-table {
    font-size: 14px;
  }

  .premium-table th,
  .premium-table td {
    padding: 12px 8px;
  }
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Enhanced Button Styles */
.btn-premium {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-premium:hover::before {
  left: 100%;
}

/* Loading Animation */
.btn-premium.loading {
  pointer-events: none;
  opacity: 0.7;
}

.btn-premium.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-light: #1a202c;
    --gray-800: #e2e8f0;
    --gray-100: #2d3748;
    --gray-200: #4a5568;
  }
}

/* Print Styles */
@media print {
  .premium-sidebar,
  .sidebar-toggle,
  .btn-premium {
    display: none !important;
  }

  .premium-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }

  main {
    margin-left: 0 !important;
  }
}
