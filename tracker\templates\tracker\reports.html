{% extends 'tracker/base.html' %}

{% block title %}Reports - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Financial Reports</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button class="btn btn-outline-primary btn-sm" onclick="window.print()">
                <i class="fas fa-print"></i> Print Report
            </button>
        </div>
    </div>
</div>

<!-- Year Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>{{ current_year }} Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Month</th>
                                <th class="text-end income">Income</th>
                                <th class="text-end expense">Expenses</th>
                                <th class="text-end">Net</th>
                                <th class="text-end">Savings Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for month_data in monthly_data %}
                            <tr>
                                <td><strong>{{ month_data.month }}</strong></td>
                                <td class="text-end income">${{ month_data.income|floatformat:2 }}</td>
                                <td class="text-end expense">${{ month_data.expenses|floatformat:2 }}</td>
                                <td class="text-end {% if month_data.net >= 0 %}text-success{% else %}text-danger{% endif %}">
                                    ${{ month_data.net|floatformat:2 }}
                                </td>
                                <td class="text-end">
                                    {% if month_data.income > 0 %}
                                        {% widthratio month_data.net month_data.income 100 as savings_rate %}
                                        <span class="{% if savings_rate >= 20 %}text-success{% elif savings_rate >= 10 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ savings_rate|default:0 }}%
                                        </span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Category Breakdown -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 expense">
                    <i class="fas fa-chart-pie me-2"></i>Top Expense Categories
                </h5>
            </div>
            <div class="card-body">
                {% if expense_categories %}
                    {% for category in expense_categories %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ category.category__name }}</span>
                        <span class="expense">${{ category.total|floatformat:2 }}</span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        {% with max_expense=expense_categories.0.total %}
                            {% widthratio category.total max_expense 100 as percentage %}
                            <div class="progress-bar bg-danger" style="width: {{ percentage }}%"></div>
                        {% endwith %}
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No expense data available.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 income">
                    <i class="fas fa-chart-bar me-2"></i>Income Sources
                </h5>
            </div>
            <div class="card-body">
                {% if income_categories %}
                    {% for category in income_categories %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ category.category__name }}</span>
                        <span class="income">${{ category.total|floatformat:2 }}</span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        {% with max_income=income_categories.0.total %}
                            {% widthratio category.total max_income 100 as percentage %}
                            <div class="progress-bar bg-success" style="width: {{ percentage }}%"></div>
                        {% endwith %}
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No income data available.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Financial Health Indicators -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-heartbeat me-2"></i>Financial Health Indicators
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            {% with total_months=monthly_data|length %}
                                {% if total_months > 0 %}
                                    <h4 class="text-primary">{{ total_months }}</h4>
                                    <h6>Active Months</h6>
                                    <small class="text-muted">Months with data</small>
                                {% else %}
                                    <h4 class="text-muted">0</h4>
                                    <h6>Active Months</h6>
                                    <small class="text-muted">No data available</small>
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            {% with expense_count=0 %}
                                {% for month in monthly_data %}
                                    {% if month.expenses > 0 %}
                                        {% with expense_count=expense_count|add:1 %}{% endwith %}
                                    {% endif %}
                                {% endfor %}
                                <h4 class="text-warning">{{ expense_count }}</h4>
                                <h6>Expense Months</h6>
                                <small class="text-muted">Months with expenses</small>
                            {% endwith %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            {% with positive_months=0 %}
                                {% for month in monthly_data %}
                                    {% if month.net > 0 %}
                                        {% with positive_months=positive_months|add:1 %}{% endwith %}
                                    {% endif %}
                                {% endfor %}
                                {% with total_months=monthly_data|length %}
                                    {% if total_months > 0 %}
                                        {% widthratio positive_months total_months 100 as success_rate %}
                                        <h4 class="{% if success_rate >= 75 %}text-success{% elif success_rate >= 50 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ success_rate }}%
                                        </h4>
                                    {% else %}
                                        <h4 class="text-muted">0%</h4>
                                    {% endif %}
                                {% endwith %}
                            {% endwith %}
                            <h6>Positive Months</h6>
                            <small class="text-muted">Months with surplus</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            {% with best_month=monthly_data.0 %}
                                {% for month in monthly_data %}
                                    {% if month.net > best_month.net %}
                                        {% with best_month=month %}{% endwith %}
                                    {% endif %}
                                {% endfor %}
                                <h4 class="text-success">${{ best_month.net|floatformat:0 }}</h4>
                                <h6>Best Month</h6>
                                <small class="text-muted">{{ best_month.month }}</small>
                            {% endwith %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights and Recommendations -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Insights & Recommendations
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">
                            <i class="fas fa-thumbs-up me-1"></i>What's Working Well
                        </h6>
                        <ul class="list-unstyled">
                            {% with positive_months=0 %}
                                {% for month in monthly_data %}
                                    {% if month.net > 0 %}
                                        {% with positive_months=positive_months|add:1 %}{% endwith %}
                                    {% endif %}
                                {% endfor %}
                                {% if positive_months >= 6 %}
                                    <li><i class="fas fa-check text-success me-2"></i>Consistent positive cash flow</li>
                                {% endif %}
                            {% endwith %}
                            {% if expense_categories|length <= 5 %}
                                <li><i class="fas fa-check text-success me-2"></i>Focused spending categories</li>
                            {% endif %}
                            {% if income_categories|length >= 2 %}
                                <li><i class="fas fa-check text-success me-2"></i>Diversified income sources</li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>Areas for Improvement
                        </h6>
                        <ul class="list-unstyled">
                            {% with negative_months=0 %}
                                {% for month in monthly_data %}
                                    {% if month.net < 0 %}
                                        {% with negative_months=negative_months|add:1 %}{% endwith %}
                                    {% endif %}
                                {% endfor %}
                                {% if negative_months >= 3 %}
                                    <li><i class="fas fa-exclamation text-warning me-2"></i>Consider reducing expenses</li>
                                {% endif %}
                            {% endwith %}
                            {% if expense_categories|length >= 10 %}
                                <li><i class="fas fa-exclamation text-warning me-2"></i>Many expense categories - consider consolidating</li>
                            {% endif %}
                            {% if income_categories|length == 1 %}
                                <li><i class="fas fa-exclamation text-warning me-2"></i>Consider diversifying income sources</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
