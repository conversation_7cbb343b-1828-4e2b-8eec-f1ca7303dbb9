#!/usr/bin/env python
"""
Debug transaction creation issue
"""
import os
import sys
import django
from decimal import Decimal
from datetime import date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'financial_tracker.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from tracker.models import Category, Transaction

def debug_transaction_creation():
    print("🔍 Debugging Transaction Creation")
    print("=" * 40)
    
    # Create test user
    client = Client()
    
    # Register and login user
    registration_data = {
        'username': 'debuguser',
        'first_name': 'Debug',
        'last_name': 'User',
        'email': '<EMAIL>',
        'password1': 'testpass123!',
        'password2': 'testpass123!'
    }
    
    response = client.post('/register/', registration_data)
    print(f"Registration response: {response.status_code}")
    
    # Login
    login_data = {
        'username': 'debuguser',
        'password': 'testpass123!'
    }
    
    response = client.post('/login/', login_data)
    print(f"Login response: {response.status_code}")
    
    # Get user
    user = User.objects.get(username='debuguser')
    print(f"User created: {user.username}")
    
    # Check categories
    income_categories = Category.objects.filter(type='income')
    print(f"Income categories available: {income_categories.count()}")
    for cat in income_categories[:3]:
        print(f"  - {cat.name} (ID: {cat.id})")
    
    if income_categories.exists():
        income_category = income_categories.first()
        
        # Test add transaction page first (with type parameter)
        response = client.get('/transactions/add/?type=income')
        print(f"Add transaction page response: {response.status_code}")

        if response.status_code == 200:
            print("Add transaction page loads successfully")

            # Try to create transaction
            income_data = {
                'title': 'Debug Salary',
                'amount': '3000.00',
                'type': 'income',
                'category': income_category.id,
                'description': 'Debug salary payment',
                'date': date.today().strftime('%Y-%m-%d')
            }

            print(f"Submitting transaction data: {income_data}")

            response = client.post('/transactions/add/?type=income', income_data)
            print(f"Transaction creation response: {response.status_code}")
            
            if response.status_code != 302:
                print("Response content:")
                print(response.content.decode()[:500])
                
                # Check for form errors
                if hasattr(response, 'context') and response.context:
                    form = response.context.get('form')
                    if form and hasattr(form, 'errors'):
                        print(f"Form errors: {form.errors}")
            else:
                print("Transaction created successfully!")
                
                # Verify transaction exists
                transactions = Transaction.objects.filter(user=user, title='Debug Salary')
                print(f"Transactions found: {transactions.count()}")
                if transactions.exists():
                    transaction = transactions.first()
                    print(f"Transaction: {transaction.title} - ${transaction.amount}")
        else:
            print("Add transaction page failed to load")
    else:
        print("No income categories found!")

if __name__ == '__main__':
    debug_transaction_creation()
