#!/usr/bin/env python
"""
Debug budget creation
"""
import os
import sys
import django
from datetime import date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'financial_tracker.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from tracker.models import Category

def debug_budget():
    print("🔍 Debugging Budget Creation")
    
    # Get or create user
    user, created = User.objects.get_or_create(
        username='budgetuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Budget',
            'last_name': 'User'
        }
    )
    user.set_password('testpass123!')
    user.save()
    
    client = Client()
    client.force_login(user)
    
    # Get expense category
    expense_category = Category.objects.filter(type='expense').first()
    print(f"Using category: {expense_category.name} (ID: {expense_category.id})")
    
    # Test budget add page
    response = client.get('/budgets/add/')
    print(f"Budget add page status: {response.status_code}")
    
    if response.status_code == 200:
        # Try to create budget
        budget_data = {
            'category': expense_category.id,
            'amount': '600.00',
            'month': date.today().replace(day=1).strftime('%Y-%m')
        }
        
        print(f"Budget data: {budget_data}")
        
        response = client.post('/budgets/add/', budget_data)
        print(f"Budget creation status: {response.status_code}")
        
        if response.status_code != 302:
            print("Response content:")
            print(response.content.decode()[:1000])

if __name__ == '__main__':
    debug_budget()
