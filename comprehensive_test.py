#!/usr/bin/env python
"""
Comprehensive test script for Django Financial Tracker Application
Tests complete user workflow and all calculations
"""
import os
import sys
import django
from decimal import Decimal
from datetime import date, datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'financial_tracker.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from tracker.models import Category, Transaction, Budget

def test_complete_workflow():
    print("🚀 Comprehensive Django Financial Tracker Testing")
    print("=" * 60)
    
    # Initialize test client
    client = Client()
    
    # Test 1: User Registration Workflow
    print("\n1. Testing User Registration Workflow...")
    
    # Test registration page loads
    response = client.get('/register/')
    assert response.status_code == 200, "Registration page should load"
    print("   ✅ Registration page loads correctly")
    
    # Test user registration
    registration_data = {
        'username': 'newuser',
        'first_name': 'New',
        'last_name': 'User',
        'email': '<EMAIL>',
        'password1': 'testpass123!',
        'password2': 'testpass123!'
    }
    
    response = client.post('/register/', registration_data)
    assert response.status_code == 302, "Registration should redirect after success"
    print("   ✅ User registration works correctly")
    
    # Verify user was created
    new_user = User.objects.get(username='newuser')
    assert new_user.email == '<EMAIL>', "User email should be saved"
    print("   ✅ User data saved correctly")
    
    # Test 2: Login Workflow
    print("\n2. Testing Login Workflow...")
    
    # Test login page loads
    response = client.get('/login/')
    assert response.status_code == 200, "Login page should load"
    print("   ✅ Login page loads correctly")
    
    # Test login
    login_data = {
        'username': 'newuser',
        'password': 'testpass123!'
    }
    
    response = client.post('/login/', login_data)
    assert response.status_code == 302, "Login should redirect after success"
    print("   ✅ User login works correctly")
    
    # Test 3: Dashboard Access
    print("\n3. Testing Dashboard Access...")
    
    response = client.get('/')
    assert response.status_code == 200, "Dashboard should be accessible after login"
    assert b'Dashboard' in response.content, "Dashboard should contain title"
    print("   ✅ Dashboard accessible and renders correctly")
    
    # Test 4: Category Management
    print("\n4. Testing Category Management...")
    
    # Test category list page
    response = client.get('/categories/')
    assert response.status_code == 200, "Category list should be accessible"
    print("   ✅ Category list page accessible")
    
    # Test add category page
    response = client.get('/categories/add/')
    assert response.status_code == 200, "Add category page should be accessible"
    print("   ✅ Add category page accessible")
    
    # Test creating a new category
    category_data = {
        'name': 'Test Category',
        'type': 'expense',
        'description': 'A test category for testing'
    }
    
    response = client.post('/categories/add/', category_data)
    assert response.status_code == 302, "Category creation should redirect"
    
    # Verify category was created
    test_category = Category.objects.get(name='Test Category')
    assert test_category.type == 'expense', "Category type should be saved correctly"
    print("   ✅ Category creation works correctly")
    
    # Test 5: Transaction Management
    print("\n5. Testing Transaction Management...")
    
    # Test transaction list page
    response = client.get('/transactions/')
    assert response.status_code == 200, "Transaction list should be accessible"
    print("   ✅ Transaction list page accessible")
    
    # Test add transaction page
    response = client.get('/transactions/add/?type=income')
    assert response.status_code == 200, "Add transaction page should be accessible"
    print("   ✅ Add transaction page accessible")

    # Test creating income transaction
    income_category = Category.objects.filter(type='income').first()
    income_data = {
        'title': 'Test Salary',
        'amount': '3000.00',
        'type': 'income',
        'category': income_category.id,
        'description': 'Test salary payment',
        'date': date.today().strftime('%Y-%m-%d')
    }

    response = client.post('/transactions/add/?type=income', income_data)
    assert response.status_code == 302, "Income transaction creation should redirect"
    
    # Verify transaction was created
    income_transaction = Transaction.objects.get(title='Test Salary', user=new_user)
    assert income_transaction.amount == Decimal('3000.00'), "Transaction amount should be saved correctly"
    print("   ✅ Income transaction creation works correctly")
    
    # Test creating expense transaction
    expense_data = {
        'title': 'Test Groceries',
        'amount': '200.00',
        'type': 'expense',
        'category': test_category.id,
        'description': 'Test grocery shopping',
        'date': date.today().strftime('%Y-%m-%d')
    }

    response = client.post('/transactions/add/?type=expense', expense_data)
    assert response.status_code == 302, "Expense transaction creation should redirect"
    
    # Verify transaction was created
    expense_transaction = Transaction.objects.get(title='Test Groceries', user=new_user)
    assert expense_transaction.amount == Decimal('200.00'), "Expense amount should be saved correctly"
    print("   ✅ Expense transaction creation works correctly")
    
    # Test 6: Budget Management
    print("\n6. Testing Budget Management...")
    
    # Test budget list page
    response = client.get('/budgets/')
    assert response.status_code == 200, "Budget list should be accessible"
    print("   ✅ Budget list page accessible")
    
    # Test add budget page
    response = client.get('/budgets/add/')
    assert response.status_code == 200, "Add budget page should be accessible"
    print("   ✅ Add budget page accessible")
    
    # Test creating a budget
    budget_data = {
        'category': test_category.id,
        'amount': '500.00',
        'month': date.today().replace(day=1).strftime('%Y-%m')
    }
    
    response = client.post('/budgets/add/', budget_data)
    assert response.status_code == 302, "Budget creation should redirect"
    
    # Verify budget was created and calculations work
    test_budget = Budget.objects.get(category=test_category, user=new_user)
    assert test_budget.amount == Decimal('500.00'), "Budget amount should be saved correctly"
    
    spent = test_budget.spent_amount()
    remaining = test_budget.remaining_amount()
    over_budget = test_budget.is_over_budget()
    
    assert spent == Decimal('200.00'), f"Spent amount should be 200.00, got {spent}"
    assert remaining == Decimal('300.00'), f"Remaining should be 300.00, got {remaining}"
    assert not over_budget, "Should not be over budget"
    
    print("   ✅ Budget creation and calculations work correctly")
    
    # Test 7: Reports Page
    print("\n7. Testing Reports Page...")
    
    response = client.get('/reports/')
    assert response.status_code == 200, "Reports page should be accessible"
    assert b'Financial Reports' in response.content, "Reports page should contain title"
    print("   ✅ Reports page accessible and renders correctly")
    
    # Test 8: Financial Calculations
    print("\n8. Testing Financial Calculations...")
    
    # Test dashboard calculations
    response = client.get('/')
    
    # Verify the dashboard loads with our test data
    user_transactions = Transaction.objects.filter(user=new_user)
    total_income = sum(t.amount for t in user_transactions.filter(type='income'))
    total_expenses = sum(t.amount for t in user_transactions.filter(type='expense'))
    balance = total_income - total_expenses
    
    assert total_income >= Decimal('3000.00'), f"Total income should include our test transaction, got {total_income}"
    assert total_expenses >= Decimal('200.00'), f"Total expenses should include our test transaction, got {total_expenses}"
    assert balance == total_income - total_expenses, "Balance calculation should be correct"
    
    print(f"   ✅ Total Income: ${total_income}")
    print(f"   ✅ Total Expenses: ${total_expenses}")
    print(f"   ✅ Balance: ${balance}")
    print("   ✅ Financial calculations are correct")
    
    # Test 9: Transaction Editing
    print("\n9. Testing Transaction Editing...")
    
    # Test edit transaction page
    response = client.get(f'/transactions/{expense_transaction.id}/edit/')
    assert response.status_code == 200, "Edit transaction page should be accessible"
    print("   ✅ Edit transaction page accessible")
    
    # Test updating transaction
    updated_data = {
        'title': 'Updated Test Groceries',
        'amount': '250.00',
        'type': 'expense',
        'category': test_category.id,
        'description': 'Updated test grocery shopping',
        'date': date.today().strftime('%Y-%m-%d')
    }
    
    response = client.post(f'/transactions/{expense_transaction.id}/edit/', updated_data)
    assert response.status_code == 302, "Transaction update should redirect"
    
    # Verify transaction was updated
    expense_transaction.refresh_from_db()
    assert expense_transaction.title == 'Updated Test Groceries', "Transaction title should be updated"
    assert expense_transaction.amount == Decimal('250.00'), "Transaction amount should be updated"
    print("   ✅ Transaction editing works correctly")
    
    # Test 10: Transaction Deletion
    print("\n10. Testing Transaction Deletion...")
    
    # Test delete confirmation page
    response = client.get(f'/transactions/{expense_transaction.id}/delete/')
    assert response.status_code == 200, "Delete confirmation page should be accessible"
    print("   ✅ Delete confirmation page accessible")
    
    # Test transaction deletion
    response = client.post(f'/transactions/{expense_transaction.id}/delete/')
    assert response.status_code == 302, "Transaction deletion should redirect"
    
    # Verify transaction was deleted
    assert not Transaction.objects.filter(id=expense_transaction.id).exists(), "Transaction should be deleted"
    print("   ✅ Transaction deletion works correctly")
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED! Application is fully functional!")
    print("\n📊 Test Summary:")
    print("   ✅ User Registration & Authentication")
    print("   ✅ Dashboard & Navigation")
    print("   ✅ Category Management")
    print("   ✅ Transaction Management (CRUD)")
    print("   ✅ Budget Management & Calculations")
    print("   ✅ Financial Reports")
    print("   ✅ All Mathematical Calculations")
    print("   ✅ Template Rendering")
    print("   ✅ Database Operations")
    print("   ✅ Form Validation & Processing")
    
    print("\n🚀 The Django Financial Tracker is ready for use!")
    print("   Visit: http://127.0.0.1:8000/")
    print("   Admin: http://127.0.0.1:8000/admin/")
    print("   Login: admin / admin123")

if __name__ == '__main__':
    test_complete_workflow()
