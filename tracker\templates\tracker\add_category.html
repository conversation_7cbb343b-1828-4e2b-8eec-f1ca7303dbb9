{% extends 'tracker/base.html' %}

{% block title %}Add Category - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Add Category</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'category_list' %}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to Categories
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    Create New Category
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Category Name *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger">{{ form.name.errors }}</div>
                        {% endif %}
                        <div class="form-text">Choose a descriptive name for your category (e.g., "Groceries", "Salary", "Entertainment")</div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.type.id_for_label }}" class="form-label">Category Type *</label>
                        {{ form.type }}
                        {% if form.type.errors %}
                            <div class="text-danger">{{ form.type.errors }}</div>
                        {% endif %}
                        <div class="form-text">
                            <span class="income"><i class="fas fa-arrow-up"></i> Income:</span> Money you receive (salary, freelance, investments)<br>
                            <span class="expense"><i class="fas fa-arrow-down"></i> Expense:</span> Money you spend (bills, food, entertainment)
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">{{ form.description.errors }}</div>
                        {% endif %}
                        <div class="form-text">Optional: Add more details about this category</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'category_list' %}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Category
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Category Examples -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Category Examples
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="income">
                            <i class="fas fa-arrow-up me-1"></i>Income Categories
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-circle fa-xs income me-2"></i>Salary</li>
                            <li><i class="fas fa-circle fa-xs income me-2"></i>Freelance</li>
                            <li><i class="fas fa-circle fa-xs income me-2"></i>Investments</li>
                            <li><i class="fas fa-circle fa-xs income me-2"></i>Side Business</li>
                            <li><i class="fas fa-circle fa-xs income me-2"></i>Gifts Received</li>
                            <li><i class="fas fa-circle fa-xs income me-2"></i>Refunds</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="expense">
                            <i class="fas fa-arrow-down me-1"></i>Expense Categories
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-circle fa-xs expense me-2"></i>Groceries</li>
                            <li><i class="fas fa-circle fa-xs expense me-2"></i>Rent/Mortgage</li>
                            <li><i class="fas fa-circle fa-xs expense me-2"></i>Utilities</li>
                            <li><i class="fas fa-circle fa-xs expense me-2"></i>Transportation</li>
                            <li><i class="fas fa-circle fa-xs expense me-2"></i>Entertainment</li>
                            <li><i class="fas fa-circle fa-xs expense me-2"></i>Healthcare</li>
                            <li><i class="fas fa-circle fa-xs expense me-2"></i>Dining Out</li>
                            <li><i class="fas fa-circle fa-xs expense me-2"></i>Shopping</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add visual feedback when category type is selected
    document.getElementById('id_type').addEventListener('change', function() {
        const type = this.value;
        const card = document.querySelector('.card');
        
        // Remove existing classes
        card.classList.remove('border-success', 'border-danger');
        
        // Add appropriate border color
        if (type === 'income') {
            card.classList.add('border-success');
        } else if (type === 'expense') {
            card.classList.add('border-danger');
        }
    });
</script>
{% endblock %}
