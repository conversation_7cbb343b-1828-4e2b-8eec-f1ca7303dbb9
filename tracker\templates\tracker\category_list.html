{% extends 'tracker/base.html' %}

{% block title %}Categories - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Categories</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'add_category' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add Category
            </a>
        </div>
    </div>
</div>

<!-- Categories Overview -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 income">
                    <i class="fas fa-arrow-up me-2"></i>Income Categories
                </h5>
            </div>
            <div class="card-body">
                {% with income_categories=categories|dictsort:"type" %}
                    {% for category in income_categories %}
                        {% if category.type == 'income' %}
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <div>
                                    <h6 class="mb-0">{{ category.name }}</h6>
                                    {% if category.description %}
                                        <small class="text-muted">{{ category.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </div>
                                <div>
                                    <span class="badge bg-success">{{ category.get_type_display }}</span>
                                </div>
                            </div>
                        {% endif %}
                    {% empty %}
                        <p class="text-muted">No income categories yet.</p>
                    {% endfor %}
                {% endwith %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 expense">
                    <i class="fas fa-arrow-down me-2"></i>Expense Categories
                </h5>
            </div>
            <div class="card-body">
                {% with expense_categories=categories|dictsort:"type" %}
                    {% for category in expense_categories %}
                        {% if category.type == 'expense' %}
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <div>
                                    <h6 class="mb-0">{{ category.name }}</h6>
                                    {% if category.description %}
                                        <small class="text-muted">{{ category.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </div>
                                <div>
                                    <span class="badge bg-danger">{{ category.get_type_display }}</span>
                                </div>
                            </div>
                        {% endif %}
                    {% empty %}
                        <p class="text-muted">No expense categories yet.</p>
                    {% endfor %}
                {% endwith %}
            </div>
        </div>
    </div>
</div>

<!-- All Categories Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">All Categories</h5>
    </div>
    <div class="card-body">
        {% if categories %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr>
                            <td>
                                <strong>{{ category.name }}</strong>
                            </td>
                            <td>
                                <span class="badge {% if category.type == 'income' %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ category.get_type_display }}
                                </span>
                            </td>
                            <td>{{ category.description|truncatechars:80|default:"-" }}</td>
                            <td>{{ category.created_at|date:"M d, Y" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary btn-sm" disabled>
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <small class="text-muted d-block">Edit/Delete coming soon</small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h5>No categories found</h5>
                <p class="text-muted">Start by creating your first category.</p>
                <a href="{% url 'add_category' %}" class="btn btn-primary">Add Category</a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
