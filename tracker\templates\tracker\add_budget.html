{% extends 'tracker/base.html' %}

{% block title %}Add Budget - Personal Financial Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Add Budget</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'budget_list' %}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to Budgets
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Create New Budget
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.category.id_for_label }}" class="form-label">Category *</label>
                        {{ form.category }}
                        {% if form.category.errors %}
                            <div class="text-danger">{{ form.category.errors }}</div>
                        {% endif %}
                        <div class="form-text">
                            Select an expense category to set a budget for. 
                            <a href="{% url 'add_category' %}" target="_blank">Create a new category</a> if needed.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.amount.id_for_label }}" class="form-label">Budget Amount *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            {{ form.amount }}
                        </div>
                        {% if form.amount.errors %}
                            <div class="text-danger">{{ form.amount.errors }}</div>
                        {% endif %}
                        <div class="form-text">Set the maximum amount you want to spend in this category for the selected month.</div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.month.id_for_label }}" class="form-label">Month *</label>
                        {{ form.month }}
                        {% if form.month.errors %}
                            <div class="text-danger">{{ form.month.errors }}</div>
                        {% endif %}
                        <div class="form-text">Choose the month this budget applies to.</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'budget_list' %}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Budget
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Budget Guidelines -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Budget Guidelines
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info" role="alert">
                    <strong>50/30/20 Rule:</strong> A popular budgeting guideline
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary">50%</h4>
                            <h6>Needs</h6>
                            <small class="text-muted">Rent, groceries, utilities, minimum debt payments</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-warning">30%</h4>
                            <h6>Wants</h6>
                            <small class="text-muted">Entertainment, dining out, hobbies, shopping</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-success">20%</h4>
                            <h6>Savings</h6>
                            <small class="text-muted">Emergency fund, retirement, debt repayment</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Budget Amounts -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Sample Monthly Budget Amounts
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Essential Categories</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-home me-2"></i>Rent/Mortgage: $800-2000</li>
                            <li><i class="fas fa-shopping-cart me-2"></i>Groceries: $200-500</li>
                            <li><i class="fas fa-bolt me-2"></i>Utilities: $100-300</li>
                            <li><i class="fas fa-car me-2"></i>Transportation: $150-400</li>
                            <li><i class="fas fa-phone me-2"></i>Phone: $30-100</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">Discretionary Categories</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-utensils me-2"></i>Dining Out: $100-300</li>
                            <li><i class="fas fa-film me-2"></i>Entertainment: $50-200</li>
                            <li><i class="fas fa-shopping-bag me-2"></i>Shopping: $100-400</li>
                            <li><i class="fas fa-dumbbell me-2"></i>Fitness: $30-100</li>
                            <li><i class="fas fa-gift me-2"></i>Gifts: $50-150</li>
                        </ul>
                    </div>
                </div>
                <div class="alert alert-light mt-3" role="alert">
                    <small><i class="fas fa-info-circle me-1"></i>
                    These are general guidelines. Adjust amounts based on your income, location, and personal priorities.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set default month to current month
    document.addEventListener('DOMContentLoaded', function() {
        const monthInput = document.getElementById('id_month');
        if (!monthInput.value) {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            monthInput.value = `${year}-${month}`;
        }
    });

    // Add visual feedback for budget amount
    document.getElementById('id_amount').addEventListener('input', function() {
        const amount = parseFloat(this.value);
        const card = document.querySelector('.card');
        
        // Remove existing classes
        card.classList.remove('border-success', 'border-warning', 'border-danger');
        
        // Add appropriate border color based on amount
        if (amount > 0 && amount <= 100) {
            card.classList.add('border-success');
        } else if (amount > 100 && amount <= 500) {
            card.classList.add('border-warning');
        } else if (amount > 500) {
            card.classList.add('border-danger');
        }
    });
</script>
{% endblock %}
