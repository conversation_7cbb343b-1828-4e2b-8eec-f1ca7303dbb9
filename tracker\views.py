from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login
from django.contrib import messages
from django.db.models import Sum, Q
from django.utils import timezone
from datetime import date, datetime
from decimal import Decimal
from .models import Transaction, Category, Budget
from .forms import TransactionForm, CategoryForm, BudgetForm, CustomUserCreationForm

def register(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, 'Registration successful!')
            return redirect('dashboard')
    else:
        form = CustomUserCreationForm()
    return render(request, 'registration/register.html', {'form': form})

@login_required
def dashboard(request):
    # Get current month's data
    current_month = date.today().replace(day=1)
    
    # Calculate totals
    total_income = Transaction.objects.filter(
        user=request.user, 
        type='income'
    ).aggregate(Sum('amount'))['amount__sum'] or Decimal('0')
    
    total_expenses = Transaction.objects.filter(
        user=request.user, 
        type='expense'
    ).aggregate(Sum('amount'))['amount__sum'] or Decimal('0')
    
    balance = total_income - total_expenses
    
    # Current month data
    month_income = Transaction.objects.filter(
        user=request.user,
        type='income',
        date__year=current_month.year,
        date__month=current_month.month
    ).aggregate(Sum('amount'))['amount__sum'] or Decimal('0')
    
    month_expenses = Transaction.objects.filter(
        user=request.user,
        type='expense',
        date__year=current_month.year,
        date__month=current_month.month
    ).aggregate(Sum('amount'))['amount__sum'] or Decimal('0')
    
    # Recent transactions
    recent_transactions = Transaction.objects.filter(user=request.user)[:10]
    
    # Budget status
    budgets = Budget.objects.filter(
        user=request.user,
        month=current_month
    )
    
    context = {
        'total_income': total_income,
        'total_expenses': total_expenses,
        'balance': balance,
        'month_income': month_income,
        'month_expenses': month_expenses,
        'recent_transactions': recent_transactions,
        'budgets': budgets,
        'current_month': current_month,
    }
    return render(request, 'tracker/dashboard.html', context)

@login_required
def transaction_list(request):
    transactions = Transaction.objects.filter(user=request.user)
    
    # Filter by type if specified
    transaction_type = request.GET.get('type')
    if transaction_type in ['income', 'expense']:
        transactions = transactions.filter(type=transaction_type)
    
    # Filter by category if specified
    category_id = request.GET.get('category')
    if category_id:
        transactions = transactions.filter(category_id=category_id)
    
    # Filter by date range if specified
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        transactions = transactions.filter(date__gte=date_from)
    if date_to:
        transactions = transactions.filter(date__lte=date_to)
    
    categories = Category.objects.all()
    
    context = {
        'transactions': transactions,
        'categories': categories,
        'current_type': transaction_type,
        'current_category': category_id,
        'date_from': date_from,
        'date_to': date_to,
    }
    return render(request, 'tracker/transaction_list.html', context)

@login_required
def add_transaction(request):
    transaction_type = request.GET.get('type', 'expense')
    
    if request.method == 'POST':
        form = TransactionForm(request.POST, transaction_type=transaction_type)
        if form.is_valid():
            transaction = form.save(commit=False)
            transaction.user = request.user
            transaction.save()
            messages.success(request, f'{transaction.get_type_display()} added successfully!')
            return redirect('transaction_list')
    else:
        form = TransactionForm(transaction_type=transaction_type)
    
    return render(request, 'tracker/add_transaction.html', {
        'form': form,
        'transaction_type': transaction_type
    })

@login_required
def edit_transaction(request, pk):
    transaction = get_object_or_404(Transaction, pk=pk, user=request.user)
    
    if request.method == 'POST':
        form = TransactionForm(request.POST, instance=transaction)
        if form.is_valid():
            form.save()
            messages.success(request, 'Transaction updated successfully!')
            return redirect('transaction_list')
    else:
        form = TransactionForm(instance=transaction)
    
    return render(request, 'tracker/edit_transaction.html', {
        'form': form,
        'transaction': transaction
    })

@login_required
def delete_transaction(request, pk):
    transaction = get_object_or_404(Transaction, pk=pk, user=request.user)
    
    if request.method == 'POST':
        transaction.delete()
        messages.success(request, 'Transaction deleted successfully!')
        return redirect('transaction_list')
    
    return render(request, 'tracker/delete_transaction.html', {
        'transaction': transaction
    })

@login_required
def category_list(request):
    categories = Category.objects.all()
    return render(request, 'tracker/category_list.html', {'categories': categories})

@login_required
def add_category(request):
    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Category added successfully!')
            return redirect('category_list')
    else:
        form = CategoryForm()
    
    return render(request, 'tracker/add_category.html', {'form': form})

@login_required
def budget_list(request):
    budgets = Budget.objects.filter(user=request.user)
    return render(request, 'tracker/budget_list.html', {'budgets': budgets})

@login_required
def add_budget(request):
    if request.method == 'POST':
        form = BudgetForm(request.POST)
        if form.is_valid():
            budget = form.save(commit=False)
            budget.user = request.user
            budget.save()
            messages.success(request, 'Budget added successfully!')
            return redirect('budget_list')
    else:
        form = BudgetForm()
    
    return render(request, 'tracker/add_budget.html', {'form': form})

@login_required
def reports(request):
    # Monthly expense breakdown
    current_year = date.today().year
    monthly_data = []
    
    for month in range(1, 13):
        month_expenses = Transaction.objects.filter(
            user=request.user,
            type='expense',
            date__year=current_year,
            date__month=month
        ).aggregate(Sum('amount'))['amount__sum'] or Decimal('0')
        
        month_income = Transaction.objects.filter(
            user=request.user,
            type='income',
            date__year=current_year,
            date__month=month
        ).aggregate(Sum('amount'))['amount__sum'] or Decimal('0')
        
        monthly_data.append({
            'month': datetime(current_year, month, 1).strftime('%B'),
            'income': month_income,
            'expenses': month_expenses,
            'net': month_income - month_expenses
        })
    
    # Category breakdown
    expense_categories = Transaction.objects.filter(
        user=request.user,
        type='expense'
    ).values('category__name').annotate(
        total=Sum('amount')
    ).order_by('-total')
    
    income_categories = Transaction.objects.filter(
        user=request.user,
        type='income'
    ).values('category__name').annotate(
        total=Sum('amount')
    ).order_by('-total')
    
    context = {
        'monthly_data': monthly_data,
        'expense_categories': expense_categories,
        'income_categories': income_categories,
        'current_year': current_year,
    }
    return render(request, 'tracker/reports.html', context)
