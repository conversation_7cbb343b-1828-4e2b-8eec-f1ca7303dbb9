#!/usr/bin/env python
"""
Populate Django Financial Tracker with comprehensive test data
Creates test user and realistic financial data for demonstration
"""
import os
import sys
import django
from decimal import Decimal
from datetime import date, datetime, timedelta
import random

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'financial_tracker.settings')
django.setup()

from django.contrib.auth.models import User
from tracker.models import Category, Transaction, Budget

def create_test_user():
    """Create the test user with credentials test/11223344"""
    print("👤 Creating test user...")
    
    # Delete existing test user if exists
    User.objects.filter(username='test').delete()
    
    # Create test user
    test_user = User.objects.create_user(
        username='test',
        email='<EMAIL>',
        password='11223344',
        first_name='Test',
        last_name='User'
    )
    
    print(f"   ✅ Test user created: {test_user.username}")
    print(f"   📧 Email: {test_user.email}")
    print(f"   🔑 Password: 11223344")
    
    return test_user

def create_realistic_transactions(user):
    """Create realistic transaction data for the past 6 months"""
    print("💰 Creating realistic transaction data...")
    
    # Get categories
    income_categories = list(Category.objects.filter(type='income'))
    expense_categories = list(Category.objects.filter(type='expense'))
    
    # Define realistic transaction patterns
    monthly_salary = Decimal('4500.00')
    freelance_income = [Decimal('800.00'), Decimal('1200.00'), Decimal('600.00')]
    
    # Monthly expense patterns
    monthly_expenses = {
        'Rent/Mortgage': Decimal('1200.00'),
        'Utilities': Decimal('150.00'),
        'Phone': Decimal('80.00'),
        'Insurance': Decimal('200.00'),
        'Groceries': [Decimal('120.00'), Decimal('95.00'), Decimal('110.00'), Decimal('85.00')],  # Weekly
        'Transportation': [Decimal('45.00'), Decimal('52.00'), Decimal('38.00')],  # Gas fills
        'Dining Out': [Decimal('25.00'), Decimal('35.00'), Decimal('42.00'), Decimal('28.00')],
        'Entertainment': [Decimal('15.00'), Decimal('25.00'), Decimal('45.00')],
        'Shopping': [Decimal('75.00'), Decimal('120.00'), Decimal('200.00')],
        'Healthcare': [Decimal('50.00'), Decimal('25.00')],
        'Fitness': Decimal('45.00'),
    }
    
    transactions_created = 0
    
    # Create transactions for the past 6 months
    for month_offset in range(6):
        current_date = date.today().replace(day=1) - timedelta(days=30 * month_offset)
        month_start = current_date.replace(day=1)
        
        # Add monthly salary
        salary_category = next((c for c in income_categories if c.name == 'Salary'), income_categories[0])
        salary_date = month_start + timedelta(days=random.randint(25, 28))
        
        Transaction.objects.create(
            user=user,
            title=f'Monthly Salary - {salary_date.strftime("%B %Y")}',
            amount=monthly_salary,
            type='income',
            category=salary_category,
            description='Regular monthly salary payment',
            date=salary_date
        )
        transactions_created += 1
        
        # Add occasional freelance income
        if random.random() < 0.6:  # 60% chance of freelance income
            freelance_category = next((c for c in income_categories if c.name == 'Freelance'), income_categories[1])
            freelance_amount = random.choice(freelance_income)
            freelance_date = month_start + timedelta(days=random.randint(5, 20))
            
            Transaction.objects.create(
                user=user,
                title=f'Freelance Project - {freelance_date.strftime("%B")}',
                amount=freelance_amount,
                type='income',
                category=freelance_category,
                description='Freelance web development project',
                date=freelance_date
            )
            transactions_created += 1
        
        # Add monthly expenses
        for expense_name, amounts in monthly_expenses.items():
            expense_category = next((c for c in expense_categories if c.name == expense_name), None)
            if not expense_category:
                continue
                
            if isinstance(amounts, list):
                # Multiple transactions per month (like groceries, gas, etc.)
                for i, amount in enumerate(amounts):
                    if random.random() < 0.8:  # 80% chance for each transaction
                        transaction_date = month_start + timedelta(days=random.randint(1, 28))
                        
                        # Add some variation to amounts
                        varied_amount = amount + Decimal(str(random.uniform(-10, 10))).quantize(Decimal('0.01'))
                        if varied_amount < Decimal('5.00'):
                            varied_amount = Decimal('5.00')
                        
                        Transaction.objects.create(
                            user=user,
                            title=f'{expense_name} - {transaction_date.strftime("%b %d")}',
                            amount=varied_amount,
                            type='expense',
                            category=expense_category,
                            description=f'{expense_name} expense',
                            date=transaction_date
                        )
                        transactions_created += 1
            else:
                # Single monthly transaction
                transaction_date = month_start + timedelta(days=random.randint(1, 28))
                
                # Add some variation to amounts
                varied_amount = amounts + Decimal(str(random.uniform(-20, 20))).quantize(Decimal('0.01'))
                if varied_amount < Decimal('10.00'):
                    varied_amount = Decimal('10.00')
                
                Transaction.objects.create(
                    user=user,
                    title=f'{expense_name} - {transaction_date.strftime("%B %Y")}',
                    amount=varied_amount,
                    type='expense',
                    category=expense_category,
                    description=f'Monthly {expense_name.lower()} payment',
                    date=transaction_date
                )
                transactions_created += 1
    
    print(f"   ✅ Created {transactions_created} realistic transactions")
    return transactions_created

def create_budgets(user):
    """Create realistic budgets for the current month"""
    print("📊 Creating monthly budgets...")
    
    # Delete existing budgets for test user
    Budget.objects.filter(user=user).delete()
    
    current_month = date.today().replace(day=1)
    expense_categories = Category.objects.filter(type='expense')
    
    # Realistic budget amounts
    budget_amounts = {
        'Groceries': Decimal('400.00'),
        'Dining Out': Decimal('150.00'),
        'Transportation': Decimal('200.00'),
        'Entertainment': Decimal('100.00'),
        'Shopping': Decimal('300.00'),
        'Utilities': Decimal('180.00'),
        'Healthcare': Decimal('100.00'),
        'Fitness': Decimal('50.00'),
        'Phone': Decimal('90.00'),
    }
    
    budgets_created = 0
    
    for category_name, amount in budget_amounts.items():
        category = expense_categories.filter(name=category_name).first()
        if category:
            Budget.objects.create(
                user=user,
                category=category,
                amount=amount,
                month=current_month
            )
            budgets_created += 1
    
    print(f"   ✅ Created {budgets_created} monthly budgets")
    return budgets_created

def populate_all_data():
    """Main function to populate all test data"""
    print("🚀 Populating Django Financial Tracker with Test Data")
    print("=" * 60)
    
    # Create test user
    test_user = create_test_user()
    
    # Create realistic transactions
    transactions_count = create_realistic_transactions(test_user)
    
    # Create budgets
    budgets_count = create_budgets(test_user)
    
    # Calculate summary statistics
    user_transactions = Transaction.objects.filter(user=test_user)
    total_income = sum(t.amount for t in user_transactions.filter(type='income'))
    total_expenses = sum(t.amount for t in user_transactions.filter(type='expense'))
    balance = total_income - total_expenses
    
    print("\n" + "=" * 60)
    print("🎉 Test Data Population Complete!")
    print("\n📊 Data Summary:")
    print(f"   👤 Test User: test")
    print(f"   🔑 Password: 11223344")
    print(f"   📧 Email: <EMAIL>")
    print(f"   💰 Transactions: {transactions_count}")
    print(f"   📋 Categories: {Category.objects.count()}")
    print(f"   📊 Budgets: {budgets_count}")
    print(f"   💵 Total Income: ${total_income:,.2f}")
    print(f"   💸 Total Expenses: ${total_expenses:,.2f}")
    print(f"   💰 Balance: ${balance:,.2f}")
    
    print("\n🌐 Ready to Test!")
    print("   URL: http://127.0.0.1:8000/")
    print("   Login: test / 11223344")
    print("   Admin: admin / admin123")
    
    return True

if __name__ == '__main__':
    populate_all_data()
