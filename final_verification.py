#!/usr/bin/env python
"""
Final verification of Django Financial Tracker with test data
Tests all functionality with the populated test user and data
"""
import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'financial_tracker.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from tracker.models import Category, Transaction, Budget

def final_verification():
    print("🔍 Final Verification - Django Financial Tracker")
    print("=" * 60)
    
    # Test 1: Verify test user exists and can login
    print("\n1. Testing Test User Login...")
    
    try:
        test_user = User.objects.get(username='test')
        print(f"   ✅ Test user exists: {test_user.username}")
        print(f"   📧 Email: {test_user.email}")
    except User.DoesNotExist:
        print("   ❌ Test user not found!")
        return False
    
    # Test login
    client = Client()
    login_response = client.post('/login/', {
        'username': 'test',
        'password': '11223344'
    })
    
    if login_response.status_code == 302:
        print("   ✅ Test user login successful")
    else:
        print("   ❌ Test user login failed")
        return False
    
    # Test 2: Verify data exists
    print("\n2. Verifying Test Data...")
    
    user_transactions = Transaction.objects.filter(user=test_user)
    user_budgets = Budget.objects.filter(user=test_user)
    
    print(f"   📊 Transactions: {user_transactions.count()}")
    print(f"   📋 Budgets: {user_budgets.count()}")
    print(f"   📂 Categories: {Category.objects.count()}")
    
    if user_transactions.count() > 0:
        print("   ✅ Transaction data exists")
    else:
        print("   ❌ No transaction data found")
        return False
    
    # Test 3: Verify financial calculations
    print("\n3. Testing Financial Calculations...")
    
    total_income = sum(t.amount for t in user_transactions.filter(type='income'))
    total_expenses = sum(t.amount for t in user_transactions.filter(type='expense'))
    balance = total_income - total_expenses
    
    print(f"   💵 Total Income: ${total_income:,.2f}")
    print(f"   💸 Total Expenses: ${total_expenses:,.2f}")
    print(f"   💰 Balance: ${balance:,.2f}")
    
    if total_income > 0 and total_expenses > 0:
        print("   ✅ Financial calculations working")
    else:
        print("   ❌ Financial calculations failed")
        return False
    
    # Test 4: Test all pages with test user
    print("\n4. Testing All Pages with Test User...")
    
    pages_to_test = [
        ('/', 'Dashboard'),
        ('/transactions/', 'Transaction List'),
        ('/categories/', 'Category List'),
        ('/budgets/', 'Budget List'),
        ('/reports/', 'Reports'),
        ('/transactions/add/?type=income', 'Add Income'),
        ('/transactions/add/?type=expense', 'Add Expense'),
        ('/categories/add/', 'Add Category'),
        ('/budgets/add/', 'Add Budget'),
    ]
    
    all_pages_working = True
    for url, name in pages_to_test:
        response = client.get(url)
        if response.status_code == 200:
            print(f"   ✅ {name}: OK")
        else:
            print(f"   ❌ {name}: Status {response.status_code}")
            all_pages_working = False
    
    # Test 5: Test budget calculations
    print("\n5. Testing Budget Calculations...")
    
    budgets_working = True
    for budget in user_budgets[:3]:  # Test first 3 budgets
        spent = budget.spent_amount()
        remaining = budget.remaining_amount()
        over_budget = budget.is_over_budget()
        
        print(f"   📊 {budget.category.name}:")
        print(f"      Budget: ${budget.amount}")
        print(f"      Spent: ${spent}")
        print(f"      Remaining: ${remaining}")
        print(f"      Over budget: {over_budget}")
        
        if spent >= 0 and remaining == (budget.amount - spent):
            print(f"   ✅ {budget.category.name} calculations correct")
        else:
            print(f"   ❌ {budget.category.name} calculations incorrect")
            budgets_working = False
    
    # Test 6: Test admin user
    print("\n6. Testing Admin User...")
    
    try:
        admin_user = User.objects.get(username='admin')
        print(f"   ✅ Admin user exists: {admin_user.username}")
        print(f"   🔑 Is superuser: {admin_user.is_superuser}")
        
        # Test admin login
        admin_login = client.post('/login/', {
            'username': 'admin',
            'password': 'admin123'
        })
        
        if admin_login.status_code == 302:
            print("   ✅ Admin login successful")
        else:
            print("   ❌ Admin login failed")
    except User.DoesNotExist:
        print("   ❌ Admin user not found")
        return False
    
    # Final summary
    print("\n" + "=" * 60)
    
    if all_pages_working and budgets_working:
        print("🎉 FINAL VERIFICATION SUCCESSFUL!")
        print("\n✅ All Systems Operational:")
        print("   ✅ Test user authentication working")
        print("   ✅ All pages loading correctly")
        print("   ✅ Financial calculations accurate")
        print("   ✅ Budget tracking functional")
        print("   ✅ Database relationships intact")
        print("   ✅ Admin access working")
        
        print("\n🚀 Django Financial Tracker - READY FOR USE!")
        print("\n📱 Access Information:")
        print("   🌐 Application: http://127.0.0.1:8000/")
        print("   👤 Test User: test / 11223344")
        print("   🔧 Admin Panel: http://127.0.0.1:8000/admin/")
        print("   👨‍💼 Admin User: admin / admin123")
        
        print("\n💡 Features Available:")
        print("   📊 Dashboard with financial overview")
        print("   💰 Income & expense tracking")
        print("   📋 Category management")
        print("   📈 Budget planning & monitoring")
        print("   📊 Financial reports & analytics")
        print("   📱 Responsive mobile-friendly design")
        print("   🔒 Secure user authentication")
        print("   👥 Multi-user support")
        
        return True
    else:
        print("❌ VERIFICATION FAILED!")
        print("   Some components are not working correctly")
        return False

if __name__ == '__main__':
    success = final_verification()
    sys.exit(0 if success else 1)
