#!/usr/bin/env python
"""
Test script to verify all templates render without errors
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'financial_tracker.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User

def test_templates():
    print("🎨 Testing Template Rendering")
    print("=" * 40)
    
    client = Client()
    
    # Test public pages
    print("\n1. Testing Public Pages...")
    
    public_pages = [
        ('/login/', 'Login Page'),
        ('/register/', 'Registration Page'),
    ]
    
    for url, name in public_pages:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"   ✅ {name}: OK")
            else:
                print(f"   ❌ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    # Test authenticated pages
    print("\n2. Testing Authenticated Pages...")
    
    # Login as admin
    admin_user = User.objects.get(username='admin')
    client.force_login(admin_user)
    
    protected_pages = [
        ('/', 'Dashboard'),
        ('/transactions/', 'Transaction List'),
        ('/transactions/add/', 'Add Transaction'),
        ('/categories/', 'Category List'),
        ('/categories/add/', 'Add Category'),
        ('/budgets/', 'Budget List'),
        ('/budgets/add/', 'Add Budget'),
        ('/reports/', 'Reports'),
    ]
    
    for url, name in protected_pages:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"   ✅ {name}: OK")
            elif response.status_code == 302:
                print(f"   ⚠️  {name}: Redirect (Status {response.status_code})")
            else:
                print(f"   ❌ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    print("\n" + "=" * 40)
    print("🎉 Template testing completed!")

if __name__ == '__main__':
    test_templates()
