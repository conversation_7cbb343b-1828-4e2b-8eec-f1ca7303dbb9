from django.core.management.base import BaseCommand
from tracker.models import Category


class Command(BaseCommand):
    help = 'Create sample categories for testing the financial tracker'

    def handle(self, *args, **options):
        # Income categories
        income_categories = [
            {
                'name': 'Salary',
                'type': 'income',
                'description': 'Regular employment income'
            },
            {
                'name': 'Freelance',
                'type': 'income',
                'description': 'Freelance work and consulting'
            },
            {
                'name': 'Investments',
                'type': 'income',
                'description': 'Dividends, interest, and investment returns'
            },
            {
                'name': 'Side Business',
                'type': 'income',
                'description': 'Income from side business or projects'
            },
            {
                'name': 'Gifts Received',
                'type': 'income',
                'description': 'Money received as gifts'
            },
            {
                'name': 'Refunds',
                'type': 'income',
                'description': 'Tax refunds and other refunds'
            }
        ]

        # Expense categories
        expense_categories = [
            {
                'name': 'Groceries',
                'type': 'expense',
                'description': 'Food and household items'
            },
            {
                'name': 'Rent/Mortgage',
                'type': 'expense',
                'description': 'Housing costs'
            },
            {
                'name': 'Utilities',
                'type': 'expense',
                'description': 'Electricity, gas, water, internet'
            },
            {
                'name': 'Transportation',
                'type': 'expense',
                'description': 'Gas, public transport, car maintenance'
            },
            {
                'name': 'Entertainment',
                'type': 'expense',
                'description': 'Movies, games, subscriptions'
            },
            {
                'name': 'Healthcare',
                'type': 'expense',
                'description': 'Medical expenses and insurance'
            },
            {
                'name': 'Dining Out',
                'type': 'expense',
                'description': 'Restaurants and takeout'
            },
            {
                'name': 'Shopping',
                'type': 'expense',
                'description': 'Clothing, electronics, and other purchases'
            },
            {
                'name': 'Insurance',
                'type': 'expense',
                'description': 'Auto, health, life insurance'
            },
            {
                'name': 'Phone',
                'type': 'expense',
                'description': 'Mobile phone and internet bills'
            },
            {
                'name': 'Fitness',
                'type': 'expense',
                'description': 'Gym membership and fitness activities'
            },
            {
                'name': 'Education',
                'type': 'expense',
                'description': 'Courses, books, and learning materials'
            },
            {
                'name': 'Gifts',
                'type': 'expense',
                'description': 'Gifts for others'
            },
            {
                'name': 'Travel',
                'type': 'expense',
                'description': 'Vacation and travel expenses'
            },
            {
                'name': 'Miscellaneous',
                'type': 'expense',
                'description': 'Other expenses not categorized elsewhere'
            }
        ]

        # Create categories
        created_count = 0
        for category_data in income_categories + expense_categories:
            category, created = Category.objects.get_or_create(
                name=category_data['name'],
                type=category_data['type'],
                defaults={'description': category_data['description']}
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created {category_data["type"]} category: {category_data["name"]}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Category already exists: {category_data["name"]}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'\nSuccessfully created {created_count} new categories!')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Total categories in database: {Category.objects.count()}')
        )
